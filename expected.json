{"node_id": "8f4dafe4-0682-4476-b342-b3ff1b9c98fa", "message": "Transition Result received.", "approval_required": false, "result": [{"property_name": "stock_video_clips", "semantic_type": "array", "data_type": "array", "data": [{"property_name": "stock_video_clips", "semantic_type": "object", "data_type": "object", "data": [{"property_name": "at_time", "semantic_type": "number", "data_type": "number", "data": 0.16}, {"property_name": "url", "semantic_type": "video", "data_type": "string", "data": "https://videos.pexels.com/video-files/25920873/11920975_3840_2160_60fps.mp4"}, {"property_name": "search_terms", "semantic_type": "array", "data_type": "array", "data": [{"property_name": "search_terms", "semantic_type": "string", "data_type": "string", "data": "aerial view Amazon rainforest sunrise"}]}, {"property_name": "mimetype", "semantic_type": "string", "data_type": "string", "data": "video/mp4"}]}, {"property_name": "stock_video_clips", "semantic_type": "object", "data_type": "object", "data": [{"property_name": "at_time", "semantic_type": "number", "data_type": "number", "data": 2.96}, {"property_name": "url", "semantic_type": "video", "data_type": "string", "data": "https://videos.pexels.com/video-files/855190/855190-hd_1280_720_24fps.mp4"}, {"property_name": "search_terms", "semantic_type": "array", "data_type": "array", "data": [{"property_name": "search_terms", "semantic_type": "string", "data_type": "string", "data": "Amazon wildlife montage toucans capuchins"}]}, {"property_name": "mimetype", "semantic_type": "string", "data_type": "string", "data": "video/mp4"}]}, {"property_name": "stock_video_clips", "semantic_type": "object", "data_type": "object", "data": [{"property_name": "at_time", "semantic_type": "number", "data_type": "number", "data": 6.74}, {"property_name": "url", "semantic_type": "video", "data_type": "string", "data": "https://videos.pexels.com/video-files/27038106/12053520_3840_2160_24fps.mp4"}, {"property_name": "search_terms", "semantic_type": "array", "data_type": "array", "data": [{"property_name": "search_terms", "semantic_type": "string", "data_type": "string", "data": "capuchins playing in Amazon canopy"}]}, {"property_name": "mimetype", "semantic_type": "string", "data_type": "string", "data": "video/mp4"}]}, {"property_name": "stock_video_clips", "semantic_type": "object", "data_type": "object", "data": [{"property_name": "at_time", "semantic_type": "number", "data_type": "number", "data": 14.62}, {"property_name": "url", "semantic_type": "video", "data_type": "string", "data": "https://videos.pexels.com/video-files/10779195/10779195-uhd_3840_2160_30fps.mp4"}, {"property_name": "search_terms", "semantic_type": "array", "data_type": "array", "data": [{"property_name": "search_terms", "semantic_type": "string", "data_type": "string", "data": "deforestation Amazon rainforest contrast"}]}, {"property_name": "mimetype", "semantic_type": "string", "data_type": "string", "data": "video/mp4"}]}, {"property_name": "stock_video_clips", "semantic_type": "object", "data_type": "object", "data": [{"property_name": "at_time", "semantic_type": "number", "data_type": "number", "data": 15.8}, {"property_name": "url", "semantic_type": "video", "data_type": "string", "data": "https://videos.pexels.com/video-files/8544142/8544142-hd_1920_1080_25fps.mp4"}, {"property_name": "search_terms", "semantic_type": "array", "data_type": "array", "data": [{"property_name": "search_terms", "semantic_type": "string", "data_type": "string", "data": "Amazon conservation efforts reforestation"}]}, {"property_name": "mimetype", "semantic_type": "string", "data_type": "string", "data": "video/mp4"}]}]}], "sequence": 20, "workflow_status": "running", "status": "completed", "tool_name": "generate_stock_video", "transition_id": "transition-MCP_Stock_Video_Generation_generate_stock_video-1751032649990"}