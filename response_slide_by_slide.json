{
    "task_id": "10d4a7de-6b33-4fdb-958b-dae5f7155f4b"
}


{
  "type": "object",
  "properties": {
    "content": {
      "type": "array",
      "description": "A list of content blocks returned by the system, usually includes response text or messages.",
      "items": {
        "type": "object",
        "properties": {
          "type": {
            "type": "string",
            "description": "The type of content (e.g., 'text', 'image').",
            "enum": [
              "text"
            ]
          },
          "text": {
            "type": "string",
            "description": "The actual response content or message in text format."
          },
          "is_error": {
            "type": "boolean",
            "description": "Indicates whether this content item represents an error message."
          }
        },
        "required": [
          "type",
          "text",
          "is_error"
        ]
      }
    },
    "isError": {
      "type": "boolean",
      "description": "Indicates whether the entire response represents an error state."
    }
  },
  "required": [
    "content",
    "isError"
  ]
}





{
  "type": "array",
  "items": {
    "type": "object",
    "properties": {
      "name": {
        "type": "string",
        "description": "The name of the slide theme (e.g., 'LAVENDER', 'GRADIENT')"
      },
      "images": {
        "type": "object",
        "properties": {
          "cover": {
            "type": "string",
            "format": "uri",
            "description": "URL of the cover image for the slide theme"
          },
          "content": {
            "type": "string",
            "format": "uri",
            "description": "URL of the content image for the slide theme"
          }
        },
        "required": [
          "cover",
          "content"
        ]
      }
    },
    "required": [
      "name",
      "images"
    ]
  }
}